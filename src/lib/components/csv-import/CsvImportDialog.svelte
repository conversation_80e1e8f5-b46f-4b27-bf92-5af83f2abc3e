<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import { csvImportService, type CsvImportValidation, type CsvImportResult } from '$lib/services/csvImportService';

  // Props
  const { open = false, onClose } = $props<{
    open?: boolean;
    onClose?: () => void;
  }>();

  // 状态管理
  let currentStep = $state<'upload' | 'preview' | 'import' | 'result'>('upload');
  let selectedFile = $state<File | null>(null);
  let csvContent = $state<string>('');
  let validation = $state<CsvImportValidation | null>(null);
  let importResult = $state<CsvImportResult | null>(null);
  let isProcessing = $state(false);
  let error = $state<string | null>(null);

  // 文件上传处理
  async function handleFileSelect(event: Event) {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.csv')) {
      error = '请选择CSV文件';
      return;
    }

    selectedFile = file;
    error = null;

    try {
      csvContent = await csvImportService.readFileContent(file);
      console.log('文件读取成功，内容长度:', csvContent.length);
    } catch (err: any) {
      error = `文件读取失败: ${err.message}`;
      selectedFile = null;
    }
  }

  // 验证CSV数据
  async function validateCsvData() {
    if (!csvContent) {
      error = '没有CSV内容可验证';
      return;
    }

    isProcessing = true;
    error = null;

    try {
      // 首先验证格式
      await csvImportService.validateCsvFormat(csvContent);
      
      // 然后解析和验证数据
      validation = await csvImportService.parseCsvPersonnelData(csvContent);
      currentStep = 'preview';
    } catch (err: any) {
      error = err.message;
    } finally {
      isProcessing = false;
    }
  }

  // 执行导入
  async function executeImport() {
    if (!validation) {
      error = '没有验证数据可导入';
      return;
    }

    isProcessing = true;
    error = null;
    currentStep = 'import';

    try {
      importResult = await csvImportService.executeCsvPersonnelImport(validation);
      currentStep = 'result';
    } catch (err: any) {
      error = err.message;
      currentStep = 'preview';
    } finally {
      isProcessing = false;
    }
  }

  // 重置状态
  function resetState() {
    currentStep = 'upload';
    selectedFile = null;
    csvContent = '';
    validation = null;
    importResult = null;
    error = null;
    isProcessing = false;
  }

  // 关闭对话框
  function handleClose() {
    resetState();
    onClose?.();
  }

  // 下载模板
  async function downloadTemplate() {
    try {
      await csvImportService.downloadCsvTemplate();
    } catch (err: any) {
      error = `下载模板失败: ${err.message}`;
    }
  }

  // 返回上一步
  function goBack() {
    if (currentStep === 'preview') {
      currentStep = 'upload';
    } else if (currentStep === 'result') {
      currentStep = 'preview';
    }
  }
</script>

{#if open}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
        <h2 class="text-xl font-semibold text-slate-900 dark:text-slate-100">
          批量人员授权导入
        </h2>
        <button
          onclick={handleClose}
          class="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- 进度指示器 -->
      <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 rounded-full flex items-center justify-center {currentStep === 'upload' ? 'bg-blue-500 text-white' : 'bg-slate-200 dark:bg-slate-600 text-slate-600 dark:text-slate-300'}">
              1
            </div>
            <span class="text-sm font-medium {currentStep === 'upload' ? 'text-blue-600 dark:text-blue-400' : 'text-slate-600 dark:text-slate-300'}">
              上传文件
            </span>
          </div>
          <div class="flex-1 h-px bg-slate-200 dark:bg-slate-600"></div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 rounded-full flex items-center justify-center {currentStep === 'preview' ? 'bg-blue-500 text-white' : 'bg-slate-200 dark:bg-slate-600 text-slate-600 dark:text-slate-300'}">
              2
            </div>
            <span class="text-sm font-medium {currentStep === 'preview' ? 'text-blue-600 dark:text-blue-400' : 'text-slate-600 dark:text-slate-300'}">
              预览验证
            </span>
          </div>
          <div class="flex-1 h-px bg-slate-200 dark:bg-slate-600"></div>
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 rounded-full flex items-center justify-center {currentStep === 'import' || currentStep === 'result' ? 'bg-blue-500 text-white' : 'bg-slate-200 dark:bg-slate-600 text-slate-600 dark:text-slate-300'}">
              3
            </div>
            <span class="text-sm font-medium {currentStep === 'import' || currentStep === 'result' ? 'text-blue-600 dark:text-blue-400' : 'text-slate-600 dark:text-slate-300'}">
              导入完成
            </span>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="p-6 overflow-y-auto max-h-[60vh]">
        {#if error}
          <div class="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-red-700 dark:text-red-300">{error}</span>
            </div>
          </div>
        {/if}

        {#if currentStep === 'upload'}
          <!-- 文件上传步骤 -->
          <div class="space-y-6">
            <div class="text-center">
              <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
                选择CSV文件
              </h3>
              <p class="text-slate-600 dark:text-slate-400 mb-4">
                请选择包含人员授权信息的CSV文件进行批量导入
              </p>
            </div>

            <!-- 文件选择区域 -->
            <div class="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-8 text-center">
              <input
                type="file"
                accept=".csv"
                onchange={handleFileSelect}
                class="hidden"
                id="csv-file-input"
              />
              <label
                for="csv-file-input"
                class="cursor-pointer flex flex-col items-center space-y-4"
              >
                <svg class="w-12 h-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <div>
                  <span class="text-blue-600 dark:text-blue-400 font-medium">点击选择文件</span>
                  <span class="text-slate-600 dark:text-slate-400"> 或拖拽文件到此处</span>
                </div>
                <p class="text-sm text-slate-500 dark:text-slate-400">
                  支持 .csv 格式文件
                </p>
              </label>
            </div>

            {#if selectedFile}
              <div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <svg class="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                      <p class="font-medium text-slate-900 dark:text-slate-100">{selectedFile.name}</p>
                      <p class="text-sm text-slate-600 dark:text-slate-400">
                        {(selectedFile.size / 1024).toFixed(1)} KB
                      </p>
                    </div>
                  </div>
                  <Button
                    onclick={() => { selectedFile = null; csvContent = ''; }}
                    variant="outline"
                    size="sm"
                  >
                    移除
                  </Button>
                </div>
              </div>
            {/if}

            <!-- 模板下载 -->
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-blue-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <div class="flex-1">
                  <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-1">
                    CSV文件格式说明
                  </h4>
                  <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
                    CSV文件应包含两列：项目名称、授权人员。授权人员格式：姓名(角色),姓名(角色)
                  </p>
                  <Button
                    onclick={downloadTemplate}
                    variant="outline"
                    size="sm"
                    class="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-800"
                  >
                    下载模板文件
                  </Button>
                </div>
              </div>
            </div>
          </div>
        {/if}

        {#if currentStep === 'preview'}
          <!-- 预览验证步骤 -->
          {#if validation}
            {@const { statistics, valid_records, errors } = validation}
            <div class="space-y-6">
              <div class="text-center">
                <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
                  数据验证结果
                </h3>
                <p class="text-slate-600 dark:text-slate-400">
                  请检查以下验证结果，确认无误后可执行导入
                </p>
              </div>

              <!-- 统计信息 -->
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-slate-900 dark:text-slate-100">
                    {statistics.total_rows}
                  </div>
                  <div class="text-sm text-slate-600 dark:text-slate-400">总行数</div>
                </div>
                <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                    {statistics.successful_rows}
                  </div>
                  <div class="text-sm text-green-600 dark:text-green-400">成功行数</div>
                </div>
                <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                    {statistics.error_rows}
                  </div>
                  <div class="text-sm text-red-600 dark:text-red-400">错误行数</div>
                </div>
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {statistics.new_assignments}
                  </div>
                  <div class="text-sm text-blue-600 dark:text-blue-400">新增分配</div>
                </div>
              </div>

              <!-- 错误列表 -->
              {#if errors.length > 0}
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <h4 class="font-medium text-red-900 dark:text-red-100 mb-3">
                    验证错误 ({errors.length})
                  </h4>
                  <div class="space-y-2 max-h-40 overflow-y-auto">
                    {#each errors as error}
                      <div class="text-sm text-red-700 dark:text-red-300">
                        第 {error.row_number} 行: {error.message}
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}

              <!-- 成功记录预览 -->
              {#if valid_records.length > 0}
                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <h4 class="font-medium text-green-900 dark:text-green-100 mb-3">
                    有效记录预览 (显示前5条)
                  </h4>
                  <div class="space-y-3 max-h-60 overflow-y-auto">
                    {#each valid_records.slice(0, 5) as record}
                      <div class="bg-white dark:bg-slate-800 rounded p-3 border border-green-200 dark:border-green-700">
                        <div class="font-medium text-slate-900 dark:text-slate-100 mb-1">
                          {record.project_match.project_name}
                        </div>
                        <div class="text-sm text-slate-600 dark:text-slate-400">
                          {#each record.personnel_assignments as assignment}
                            <span class="inline-block bg-slate-100 dark:bg-slate-700 rounded px-2 py-1 mr-2 mb-1">
                              {assignment.personnel_match.name} ({assignment.role_match.role_name})
                              {#if assignment.already_exists}
                                <span class="text-orange-500">已存在</span>
                              {:else}
                                <span class="text-green-500">新增</span>
                              {/if}
                            </span>
                          {/each}
                        </div>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}
            </div>
          {/if}
        {/if}

        {#if currentStep === 'import'}
          <!-- 导入进行中 -->
          <div class="text-center space-y-4">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100">
              正在导入数据...
            </h3>
            <p class="text-slate-600 dark:text-slate-400">
              请稍候，正在将人员授权信息写入数据库
            </p>
          </div>
        {/if}

        {#if currentStep === 'result'}
          <!-- 导入结果 -->
          {#if importResult}
            <div class="space-y-6">
              <div class="text-center">
                {#if importResult.success}
                  <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <h3 class="text-lg font-medium text-green-900 dark:text-green-100 mb-2">
                    导入成功完成
                  </h3>
                {:else}
                  <div class="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <h3 class="text-lg font-medium text-red-900 dark:text-red-100 mb-2">
                    导入失败
                  </h3>
                {/if}
              </div>

              <!-- 导入统计 -->
              <div class="grid grid-cols-2 gap-4">
                <div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-slate-900 dark:text-slate-100">
                    {importResult.imported_records}
                  </div>
                  <div class="text-sm text-slate-600 dark:text-slate-400">成功导入</div>
                </div>
                <div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-slate-900 dark:text-slate-100">
                    {importResult.skipped_records}
                  </div>
                  <div class="text-sm text-slate-600 dark:text-slate-400">跳过记录</div>
                </div>
              </div>

              <!-- 错误信息 -->
              {#if importResult.errors.length > 0}
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <h4 class="font-medium text-red-900 dark:text-red-100 mb-3">
                    导入错误
                  </h4>
                  <div class="space-y-1 max-h-40 overflow-y-auto">
                    {#each importResult.errors as error}
                      <div class="text-sm text-red-700 dark:text-red-300">{error}</div>
                    {/each}
                  </div>
                </div>
              {/if}
            </div>
          {/if}
        {/if}
      </div>

      <!-- 底部按钮 -->
      <div class="flex items-center justify-between p-6 border-t border-slate-200 dark:border-slate-700">
        <div>
          {#if currentStep !== 'upload' && currentStep !== 'import'}
            <Button onclick={goBack} variant="outline">
              返回上一步
            </Button>
          {/if}
        </div>
        <div class="flex space-x-3">
          <Button onclick={handleClose} variant="outline">
            {currentStep === 'result' ? '关闭' : '取消'}
          </Button>
          {#if currentStep === 'upload'}
            <Button
              onclick={validateCsvData}
              disabled={!selectedFile || isProcessing}
              class="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isProcessing ? '验证中...' : '验证数据'}
            </Button>
          {:else if currentStep === 'preview'}
            <Button
              onclick={executeImport}
              disabled={!validation || validation.valid_records.length === 0 || isProcessing}
              class="bg-green-600 hover:bg-green-700 text-white"
            >
              执行导入
            </Button>
          {:else if currentStep === 'result'}
            <Button
              onclick={resetState}
              class="bg-blue-600 hover:bg-blue-700 text-white"
            >
              重新导入
            </Button>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}
